"""
NEUROGLYPH Multi-Hop Reasoning Tests
Test reasoning simbolico con profondità 3-12 passi
Target: Depth ≥3, Accuracy ≥90%
"""

import pytest
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from neuroglyph.logic.logic_engine import FormalLogicEngine
    from neuroglyph.logic.formula import *
    from neuroglyph.cognitive.reasoner import NGReasoner
    LOGIC_AVAILABLE = True
except ImportError:
    LOGIC_AVAILABLE = False


class TestMultiHopReasoning:
    """Test reasoning multi-hop con profondità crescente."""
    
    @pytest.fixture(scope="class")
    def logic_engine(self):
        """Inizializza Logic Engine per multi-hop."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        return FormalLogicEngine(max_depth=12, max_steps=100)
    
    @pytest.fixture(scope="class")
    def ng_reasoner(self):
        """Inizializza NGReasoner per multi-hop."""
        if not LOGIC_AVAILABLE:
            pytest.skip("NGReasoner non disponibile")
        return NGReasoner(max_depth=12, max_paths=8)
    
    def test_3_step_reasoning(self, logic_engine):
        """Test reasoning a 3 passi."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Scenario: A → B, B → C, A ⊢ C (3 passi: A, A→B quindi B, B→C quindi C)
        premises = [
            Implication(P("A"), P("B")),  # A → B
            Implication(P("B"), P("C")),  # B → C
            P("A")                        # A
        ]
        goal = P("C")
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 3-Step Reasoning Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        assert result.success, "3-step reasoning fallito"
        assert result.depth >= 3, f"Depth {result.depth} < 3"
        assert duration <= 1.0, f"Duration {duration:.3f}s > 1.0s"
        
        print(f"✅ 3-STEP REASONING SUCCESS: depth={result.depth}, duration={duration:.3f}s")
    
    def test_5_step_reasoning(self, logic_engine):
        """Test reasoning a 5 passi."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Scenario: A → B → C → D → E, A ⊢ E
        premises = [
            Implication(P("A"), P("B")),  # A → B
            Implication(P("B"), P("C")),  # B → C
            Implication(P("C"), P("D")),  # C → D
            Implication(P("D"), P("E")),  # D → E
            P("A")                        # A
        ]
        goal = P("E")
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 5-Step Reasoning Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        assert result.success, "5-step reasoning fallito"
        assert result.depth >= 5, f"Depth {result.depth} < 5"
        assert duration <= 2.0, f"Duration {duration:.3f}s > 2.0s"
        
        print(f"✅ 5-STEP REASONING SUCCESS: depth={result.depth}, duration={duration:.3f}s")
    
    def test_8_step_reasoning(self, logic_engine):
        """Test reasoning a 8 passi."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Scenario complesso con quantificatori e congiunzioni
        x = Variable("x")
        a = Constant("a")
        
        premises = [
            # ∀x (P(x) → Q(x))
            ForAll(x, Implication(P("P", x), P("Q", x))),
            # ∀x (Q(x) → R(x))
            ForAll(x, Implication(P("Q", x), P("R", x))),
            # ∀x (R(x) → S(x))
            ForAll(x, Implication(P("R", x), P("S", x))),
            # ∀x (S(x) → T(x))
            ForAll(x, Implication(P("S", x), P("T", x))),
            # P(a)
            P("P", a)
        ]
        goal = P("T", a)
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 8-Step Reasoning Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        # 8-step potrebbe essere challenging, quindi target più flessibile
        if result.success:
            assert result.depth >= 6, f"Depth {result.depth} < 6"
            assert duration <= 5.0, f"Duration {duration:.3f}s > 5.0s"
            print(f"✅ 8-STEP REASONING SUCCESS: depth={result.depth}, duration={duration:.3f}s")
        else:
            print(f"⚠️ 8-STEP REASONING FAILED (acceptable for complex reasoning)")
    
    def test_complex_syllogism_chain(self, logic_engine):
        """Test catena di sillogismi complessi."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        x = Variable("x")
        socrates = Constant("Socrates")
        
        premises = [
            # ∀x (Human(x) → Mortal(x))
            ForAll(x, Implication(P("Human", x), P("Mortal", x))),
            # ∀x (Philosopher(x) → Human(x))
            ForAll(x, Implication(P("Philosopher", x), P("Human", x))),
            # ∀x (Greek(x) ∧ Wise(x) → Philosopher(x))
            ForAll(x, Implication(
                Conjunction(P("Greek", x), P("Wise", x)),
                P("Philosopher", x)
            )),
            # Greek(Socrates)
            P("Greek", socrates),
            # Wise(Socrates)
            P("Wise", socrates)
        ]
        goal = P("Mortal", socrates)
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 Complex Syllogism Chain Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        assert result.success, "Complex syllogism chain fallito"
        assert result.depth >= 4, f"Depth {result.depth} < 4"
        assert duration <= 3.0, f"Duration {duration:.3f}s > 3.0s"
        
        print(f"✅ COMPLEX SYLLOGISM SUCCESS: depth={result.depth}, duration={duration:.3f}s")
    
    def test_mathematical_reasoning_chain(self, logic_engine):
        """Test catena di reasoning matematico."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Scenario: proprietà matematiche con catena di deduzioni
        premises = [
            # Se x è pari, allora x è divisibile per 2
            Implication(P("Even", Variable("x")), P("DivisibleBy2", Variable("x"))),
            # Se x è divisibile per 2, allora x è un numero intero
            Implication(P("DivisibleBy2", Variable("x")), P("Integer", Variable("x"))),
            # Se x è un numero intero, allora x è un numero razionale
            Implication(P("Integer", Variable("x")), P("Rational", Variable("x"))),
            # Se x è razionale, allora x è un numero reale
            Implication(P("Rational", Variable("x")), P("Real", Variable("x"))),
            # 4 è pari
            P("Even", Constant("4"))
        ]
        goal = P("Real", Constant("4"))
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 Mathematical Reasoning Chain Test:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        assert result.success, "Mathematical reasoning chain fallito"
        assert result.depth >= 4, f"Depth {result.depth} < 4"
        assert duration <= 2.0, f"Duration {duration:.3f}s > 2.0s"
        
        print(f"✅ MATHEMATICAL REASONING SUCCESS: depth={result.depth}, duration={duration:.3f}s")
    
    def test_reasoning_performance_scaling(self, logic_engine):
        """Test scaling delle performance con profondità crescente."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        test_cases = [
            {'depth': 3, 'max_duration': 1.0},
            {'depth': 5, 'max_duration': 2.0},
            {'depth': 8, 'max_duration': 5.0}
        ]
        
        performance_results = []
        
        for case in test_cases:
            depth = case['depth']
            max_duration = case['max_duration']
            
            # Genera catena di implicazioni di lunghezza depth
            premises = []
            for i in range(depth - 1):
                premises.append(Implication(P(f"P{i}"), P(f"P{i+1}")))
            premises.append(P("P0"))  # Premessa iniziale
            
            goal = P(f"P{depth-1}")  # Goal finale
            
            start_time = time.perf_counter()
            result = logic_engine.deduce(premises, goal)
            duration = time.perf_counter() - start_time
            
            performance_results.append({
                'target_depth': depth,
                'actual_depth': result.depth if result.success else 0,
                'success': result.success,
                'duration': duration,
                'max_duration': max_duration,
                'within_target': duration <= max_duration
            })
        
        # Analizza risultati
        successful_cases = [r for r in performance_results if r['success']]
        success_rate = len(successful_cases) / len(test_cases)
        
        within_target_cases = [r for r in performance_results if r['within_target']]
        performance_rate = len(within_target_cases) / len(test_cases)
        
        print(f"📊 Reasoning Performance Scaling:")
        print(f"   - Test cases: {len(test_cases)}")
        print(f"   - Successful: {len(successful_cases)} ({success_rate:.2%})")
        print(f"   - Within performance target: {len(within_target_cases)} ({performance_rate:.2%})")
        
        for result in performance_results:
            status = "✅" if result['success'] and result['within_target'] else "⚠️" if result['success'] else "❌"
            print(f"   {status} Depth {result['target_depth']}: {result['duration']:.3f}s ≤ {result['max_duration']}s")
        
        # Target: ≥80% success rate, ≥70% performance rate
        assert success_rate >= 0.80, f"Success rate {success_rate:.2%} < 80%"
        assert performance_rate >= 0.70, f"Performance rate {performance_rate:.2%} < 70%"
        
        print(f"✅ SCALING TARGETS RAGGIUNTI: success={success_rate:.2%}, performance={performance_rate:.2%}")
    
    @pytest.mark.slow
    def test_12_step_reasoning_challenge(self, logic_engine):
        """Test challenge: reasoning a 12 passi (lento)."""
        if not LOGIC_AVAILABLE:
            pytest.skip("Logic Engine non disponibile")
        
        # Scenario ultra-complesso: catena di 12 implicazioni
        premises = []
        for i in range(11):
            premises.append(Implication(P(f"Step{i}"), P(f"Step{i+1}")))
        premises.append(P("Step0"))  # Premessa iniziale
        
        goal = P("Step11")  # Goal finale dopo 12 passi
        
        start_time = time.perf_counter()
        result = logic_engine.deduce(premises, goal)
        duration = time.perf_counter() - start_time
        
        print(f"🧠 12-Step Reasoning Challenge:")
        print(f"   - Success: {result.success}")
        print(f"   - Steps: {result.steps_count}")
        print(f"   - Depth: {result.depth}")
        print(f"   - Duration: {duration:.3f}s")
        
        if result.success:
            assert result.depth >= 10, f"Depth {result.depth} < 10"
            assert duration <= 10.0, f"Duration {duration:.3f}s > 10.0s"
            print(f"🏆 12-STEP REASONING SUCCESS: NEUROGLYPH è un truly thinking LLM!")
        else:
            print(f"⚠️ 12-STEP REASONING FAILED: ancora da ottimizzare per reasoning ultra-profondo")
            # Non falliamo il test - 12 step è molto challenging
        
        # Registra il risultato per analisi future
        print(f"📊 12-Step Challenge Result: success={result.success}, depth={result.depth}, duration={duration:.3f}s")


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
